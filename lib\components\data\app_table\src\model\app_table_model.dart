import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_table_model.g.dart';

@JsonSerializable(explicitToJson: true)
class AppTableModel {
  @JsonKey(name: 'SmartTableHeaderId', defaultValue: '')
  String smartTableHeaderId;

  @JsonKey(name: 'HeaderSet', defaultValue: <AppTableColumn>[])
  List<AppTableColumn> headerSet;

  AppTableModel({required this.smartTableHeaderId, required this.headerSet});

  factory AppTableModel.fromJson(Map<String, dynamic> json) => _$AppTableModelFromJson(json);

  Map<String, dynamic> toJson() => _$AppTableModelToJson(this);
}
