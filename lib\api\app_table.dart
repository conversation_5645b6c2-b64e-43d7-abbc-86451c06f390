import 'package:octasync_client/utils/http_service.dart';

final _http = HttpService();

const controller = '/Business/SmartTableHeader/';

/// 表格组件
class AppTableApi {
  /// 获取所有已存在的表格
  static Future<dynamic> getListPage(data) {
    return _http.post('${controller}GetListPage', data: data);
  }

  // static Future<dynamic> add(data) {
  //   return _http.post('${controller}Add', data: data);
  // }

  // static Future<dynamic> delete(data) {
  //   return _http.post('${controller}Delete', data: data);
  // }

  // static Future<dynamic> edit(data) {
  //   return _http.post('${controller}Edit', data: data);
  // }

  // static Future<dynamic> getDetail(data) {
  //   return _http.post('${controller}GetDetail', data: data);
  // }

  // static Future<dynamic> setOrder(data) {
  //   return _http.post('${controller}setOrder', data: data);
  // }
}
