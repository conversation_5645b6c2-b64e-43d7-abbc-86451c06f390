// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_option.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeneralOption<T> _$GeneralOptionFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => GeneralOption<T>(
  id: fromJsonT(json['Id']),
  text: json['Text'] as String? ?? '',
  symbol: json['Symbol'] as String? ?? '',
);

Map<String, dynamic> _$GeneralOptionToJson<T>(
  GeneralOption<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'Id': toJsonT(instance.id),
  'Text': instance.text,
  'Symbol': instance.symbol,
};
