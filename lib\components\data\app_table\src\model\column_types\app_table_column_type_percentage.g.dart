// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_percentage.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypePercentage _$AppTableColumnTypePercentageFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypePercentage(
        defaultValue: json['DefaultValue'] ?? 0,
        columnDesc: json['ColumnDesc'] as String? ?? '',
        negative: json['negative'] as bool,
        format: json['format'] as String,
        applyFormatOnInit: json['applyFormatOnInit'] as bool,
        allowFirstDot: json['allowFirstDot'] as bool,
        locale: json['locale'] as String?,
        precision: (json['precision'] as num?)?.toInt() ?? 0,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.percentage;

Map<String, dynamic> _$AppTableColumnTypePercentageToJson(
  AppTableColumnTypePercentage instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'negative': instance.negative,
  'allowFirstDot': instance.allowFirstDot,
  'locale': instance.locale,
  'format': instance.format,
  'applyFormatOnInit': instance.applyFormatOnInit,
  'precision': instance.precision,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
