import 'package:json_annotation/json_annotation.dart';

part 'test_class.g.dart';

// TODO 当前类用于测试，后续需要删除
@JsonSerializable()
class TestClass {
  @Json<PERSON>ey(name: 'Id')
  final int id;

  @J<PERSON><PERSON><PERSON>(name: 'Name')
  final String name;

  @JsonKey(name: 'CreateDate')
  final DateTime? createDate;

  TestClass({required this.id, this.name = '', this.createDate});

  factory TestClass.fromJson(Map<String, dynamic> json) => _$TestClassFromJson(json);

  Map<String, dynamic> toJson() => _$TestClassToJson(this);
}
