// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_telephone.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeTelephone _$AppTableColumnTypeTelephoneFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeTelephone(
        defaultValue: json['DefaultValue'] ?? '',
        columnDesc: json['ColumnDesc'] as String? ?? '',
        telephoneType:
            $enumDecodeNullable(
              _$TelephoneEnumEnumMap,
              json['TelephoneType'],
            ) ??
            TelephoneEnum.mobile,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.telephone;

Map<String, dynamic> _$AppTableColumnTypeTelephoneToJson(
  AppTableColumnTypeTelephone instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'TelephoneType': _$TelephoneEnumEnumMap[instance.telephoneType]!,
};

const _$TelephoneEnumEnumMap = {
  TelephoneEnum.mobile: 'mobile',
  TelephoneEnum.landline: 'landline',
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
