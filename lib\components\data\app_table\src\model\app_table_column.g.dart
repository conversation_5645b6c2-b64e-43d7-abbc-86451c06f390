// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumn _$AppTableColumnFromJson(Map<String, dynamic> json) =>
    AppTableColumn(
      field: json['Field'] as String? ?? '',
      text: json['Text'] as String? ?? '',
      typeCode:
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.text,
      type: AppTableColumn._typeFromJson(json['Type'] as Map<String, dynamic>),
      width: (json['Width'] as num?)?.toDouble() ?? 200,
      minWidth: (json['MinWidth'] as num?)?.toDouble() ?? 100,
      showMore: json['ShowMore'] as bool? ?? true,
      sortable: json['Sortable'] as bool? ?? false,
      resizable: json['Resizable'] as bool? ?? false,
      frozen:
          $enumDecodeNullable(_$AppTableColumnFrozenEnumMap, json['Frozen']) ??
          AppTableColumnFrozen.none,
    );

Map<String, dynamic> _$AppTableColumnToJson(AppTableColumn instance) =>
    <String, dynamic>{
      'Field': instance.field,
      'Text': instance.text,
      'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
      'Type': AppTableColumn._typeToJson(instance.type),
      'Width': instance.width,
      'MinWidth': instance.minWidth,
      'ShowMore': instance.showMore,
      'Sortable': instance.sortable,
      'Resizable': instance.resizable,
      'Frozen': _$AppTableColumnFrozenEnumMap[instance.frozen]!,
    };

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};

const _$AppTableColumnFrozenEnumMap = {
  AppTableColumnFrozen.none: 'none',
  AppTableColumnFrozen.start: 'start',
};
