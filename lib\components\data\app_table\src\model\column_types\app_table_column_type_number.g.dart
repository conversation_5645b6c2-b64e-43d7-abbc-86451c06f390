// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_number.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeNumber _$AppTableColumnTypeNumberFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeNumber(
        defaultValue: json['DefaultValue'],
        columnDesc: json['ColumnDesc'] as String? ?? '',
        negative: json['Negative'] as bool? ?? false,
        format: json['Format'] as String,
        applyFormatOnInit: json['ApplyFormatOnInit'] as bool? ?? false,
        allowFirstDot: json['AllowFirstDot'] as bool? ?? false,
        locale: json['Locale'] as String?,
        precision: (json['Precision'] as num?)?.toInt() ?? 0,
        isShowPercentiles: json['IsShowPercentiles'] as bool? ?? false,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.number;

Map<String, dynamic> _$AppTableColumnTypeNumberToJson(
  AppTableColumnTypeNumber instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'Negative': instance.negative,
  'AllowFirstDot': instance.allowFirstDot,
  'Locale': instance.locale,
  'Format': instance.format,
  'ApplyFormatOnInit': instance.applyFormatOnInit,
  'Precision': instance.precision,
  'IsShowPercentiles': instance.isShowPercentiles,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
