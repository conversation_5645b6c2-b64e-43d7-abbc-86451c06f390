// import 'dart:ffi';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';
import 'package:octasync_client/components/data/app_table/src/app_date.dart';
import 'package:octasync_client/components/data/app_table/src/app_number.dart';
import 'package:octasync_client/components/data/app_table/src/app_select.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/telephone_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_telephone.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_text.dart';
import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';
import 'package:octasync_client/components/data/app_table/src/state/app_table_state_manage.dart';
import 'package:provider/provider.dart';
import 'package:uuid/uuid.dart';
import 'package:intl/intl.dart';
// import 'package:intl/date_symbol_data_local.dart';

class AddColumnContent extends StatefulWidget {
  double width;
  final AppTableColumn? columnModel; // 编辑时需要用到的模型
  final void Function()? onClose;
  final void Function(AppTableColumn column)? onSave;
  String? status = 'add'; //add：新增；edit：编辑

  AddColumnContent({
    super.key,
    this.width = 400,
    this.columnModel,
    this.onClose,
    this.onSave,
    this.status = 'add',
  });

  @override
  State<AddColumnContent> createState() => _AddColumnContentState();
}

class _AddColumnContentState extends State<AddColumnContent> {
  var outlineInputBorder = OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1));

  final Uuid _uuid = Uuid();

  // late TextEditingController _nameController;
  // late TextEditingController _defaultValueController;
  // late TextEditingController _columnDescController;
  // // List<TextEditingController> _optionsEditingCtrl = [];
  // Map<String, TextEditingController> _optionsEditingCtrl = {};

  double contentWidth = 400;
  double contentPadding = 15;
  // double inputHeight = 38;
  double fieldInputSpace = 5;
  double fieldSpace = 14;
  double contentBorderRadius = 8;

  double selectPanelPadding = 10;
  double horizontalSpacing = 8;
  double verticalSpacing = 0;

  // ColumnTypeEnum? hoverColumnTypeId = null;
  // ColumnTypeEnum columnModel.typeCode = ColumnTypeEnum.text; // 默认选中第一个类型
  // String? hoverSelect = null;

  AppTableColumn columnModel = AppTableGeneralHelper.newColumn(type: ColumnTypeEnum.text);

  // int? hoverPrecision = null;
  // dynamic columnTypeModel = null;

  List<GeneralOption<ColumnTypeEnum>> columnTypes = [
    GeneralOption(id: ColumnTypeEnum.text, text: '文本', icon: Icon(Icons.text_fields, size: 18)),
    GeneralOption(
      id: ColumnTypeEnum.singleSelect,
      text: '单选',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(
      id: ColumnTypeEnum.multipleSelect,
      text: '多选',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(id: ColumnTypeEnum.number, text: '数字', icon: Icon(Icons.text_fields, size: 18)),
    GeneralOption(id: ColumnTypeEnum.date, text: '日期', icon: Icon(Icons.text_fields, size: 18)),
    GeneralOption(
      id: ColumnTypeEnum.percentage,
      text: '百分比',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(
      id: ColumnTypeEnum.checkbox,
      text: '复选框',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(
      id: ColumnTypeEnum.hyperlink,
      text: '超链接',
      icon: Icon(Icons.text_fields, size: 18),
    ),

    GeneralOption(
      id: ColumnTypeEnum.parentRecord,
      text: '父记录',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(
      id: ColumnTypeEnum.telephone,
      text: '电话号码',
      icon: Icon(Icons.text_fields, size: 18),
    ),
    GeneralOption(id: ColumnTypeEnum.currency, text: '货币', icon: Icon(Icons.text_fields, size: 18)),
  ];

  List<GeneralOptionGroup<ColumnTypeEnum>> get groups => [
    GeneralOptionGroup<ColumnTypeEnum>(text: '字段类型', items: columnTypes),
  ];

  var dateFormats = AppTableCommon.dateFormats;

  /// 数据精度
  // List<GeneralOption<int>> numberTypes = [];

  // List<GeneralOption<int>> dateTypes = [];

  // ColumnTypeEnum currentColumnTypeId = ColumnTypeEnum.text; // 默认选中第一个类型

  // /// 是否为文本
  // bool get isText => columnModel.typeCode == ColumnTypeEnum.text;

  // /// 是否为复选框
  // bool get isCheckbox => columnModel.typeCode == ColumnTypeEnum.checkbox;

  // /// 是否为数字
  // bool get isNumber => columnModel.typeCode == ColumnTypeEnum.number;

  /// 是否为百分比
  // bool get isPercentage => columnModel.typeCode == ColumnTypeEnum.percentage;

  // /// 是否为单选
  // bool get isSingleSelect =>
  //     columnModel.typeCode == ColumnTypeEnum.singleSelect;

  // /// 是否为多选
  // bool get isMultipleSelect =>
  //     columnModel.typeCode == ColumnTypeEnum.multipleSelect;

  // /// 是否为电话号码
  // bool get isTelephone => columnModel.typeCode == ColumnTypeEnum.telephone;

  /// 是否为货币
  // bool get isCurrency => columnModel.typeCode == ColumnTypeEnum.currency;

  // /// 是否为日期
  // bool get isDate => columnModel.typeCode == ColumnTypeEnum.date;

  // /// 当前列类型
  // GeneralOption get currentColumnType {
  //   return columnTypes.firstWhere(
  //     (item) => item.id == columnModel.typeCode,
  //     orElse: () => columnTypes[0], // 默认返回第一个类型
  //   );
  // }

  /// 单选多选对应的选择类型
  List<GeneralOption<int>> defaultValueTypes = [
    GeneralOption(id: 1, text: '无'),
    GeneralOption(id: 2, text: '指定选项'),
  ];

  /// 日期默认值类型
  List<GeneralOption<int>> defaultDateTypes = [
    GeneralOption(id: 1, text: '无'),
    GeneralOption(id: 2, text: '指定日期'),
    GeneralOption(id: 3, text: '添加此记录的日期'),
  ];

  /// 电话号码类型
  List<GeneralOption<TelephoneEnum>> telephoneTypes = [
    GeneralOption(id: TelephoneEnum.mobile, text: '手机号码'),
    GeneralOption(id: TelephoneEnum.landline, text: '固定电话'),
  ];

  // /// 当前精度
  // GeneralOption<int> get currentPrecisionType {
  //   var result = numberTypes[0];
  //   var prec = 0;
  //   if (isNumber || isPercentage) {
  //     if (isNumber) {
  //       prec = (columnModel.type as AppTableColumnTypeNumber).precision;
  //     } else if (isPercentage) {
  //       prec = (columnModel.type as AppTableColumnTypePercentage).precision;
  //     }

  //     result = numberTypes.firstWhere(
  //       (item) => item.id == prec,
  //       orElse: () => numberTypes[0], // 默认返回第一个类型
  //     );
  //   }
  //   return result;
  // }

  @override
  void initState() {
    super.initState();

    // columnTypeModel = columnModel.type;

    // currentColumnTypeId = columnModel.typeCode;

    contentWidth = widget.width;

    // 初始化日期格式化本地化数据
    // initializeDateFormatting('zh', null);

    // _initNumberTypes();
    // _initDateTypes();

    // _nameController = TextEditingController(text: columnModel.text);

    // _columnDescController = TextEditingController(
    //   text: columnModel.type.columnDesc,
    // );
    // _defaultValueController = TextEditingController(
    //   text: columnModel.type.defaultValue,
    // );

    // if (widget.columnModel == null) {
    //   print('init---${widget.columnModel!.field}');
    // } else {
    //   print('init---没有获取到参数');
    // }
  }

  // @override
  // void didUpdateWidget(covariant AddColumnContent oldWidget) {
  //   super.didUpdateWidget(oldWidget);

  //   if (widget.columnModel == null) {
  //     print('update----${widget.columnModel!.field}');
  //   } else {
  //     print('update----没有获取到参数');
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    var mgmt = context.read<AppTableStateManage>();
    var model = mgmt.columnModel;
    if (model != null) {
      // currentColumnTypeId = model.typeCode;
      columnModel = model;
      // print('aaaaaaaaaaaa-${model.field}');
    } else {
      // print('没有值');
    }

    // var colObj = context.select<AppTableStateManage, AppTableColumn?>(
    //   (state) => state.columnModel,
    // );

    // if (colObj != null && colObj.typeCode == ColumnTypeEnum.currency) {
    //   // print('1111111111111111111111');
    // }

    // if (widget.columnModel != null) {
    //   currentColumnTypeId = widget.columnModel!.typeCode;
    //   columnModel = widget.columnModel!;
    // }

    // var selectPanelWidth = contentWidth - (contentPadding * 2);

    // var columnModelTemp = context.select<AppTableStateManage, AppTableColumn?>(
    //   (state) => state.columnModel,
    // );

    // if (columnModelTemp != null) {
    //   columnModel = columnModelTemp;
    //   currentColumnTypeId = columnModel.typeCode;
    // }

    // print('组件重新build');

    // if (columnModelTemp != null) {
    //   print('获取到编辑列对象信息——————————');
    // }

    // final columnStatus = context.select<AppTableStateManage, String>(
    //   (state) => state.columnStatus,
    // );

    // print('aaaaaaa-----${columnStatus}');

    return Container(
      width: contentWidth,
      padding: EdgeInsets.all(contentPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(contentBorderRadius),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 上面主体部分
          Container(
            padding: EdgeInsets.only(bottom: contentBorderRadius),
            child: Material(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题
                  Container(
                    child: Text(
                      '标题',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(height: fieldInputSpace),
                  AppBasicTextField(
                    // controller: _nameController,
                    initialValue: columnModel.text,
                    onChanged: (value) {
                      columnModel.text = value;
                    },
                    decoration: InputDecoration(
                      border: outlineInputBorder,
                      contentPadding: EdgeInsets.symmetric(horizontal: 10),
                      hintText: '请输入字段名称',
                    ),
                  ),
                  SizedBox(height: fieldSpace),

                  // 字段类型
                  Container(
                    child: Text(
                      '字段类型',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(height: fieldInputSpace),
                  Container(
                    child: Builder(
                      builder:
                          (innerContext) => LayoutBuilder(
                            builder: (ctx, constraints) {
                              return AppSelect<ColumnTypeEnum>(
                                key: const ValueKey('select_1'),
                                options: groups,
                                value: columnModel.typeCode,
                                onChanged: (target) {
                                  // final stateManage = context.read<AppTableStateManage>();
                                  // _changeColumnType(context, target);

                                  final stateManage = context.read<AppTableStateManage>();
                                  _changeColumnType(stateManage, target);
                                },
                              );
                            },
                          ),
                    ),
                  ),

                  SizedBox(height: fieldSpace),

                  // if (isCurrency)
                  _buildCurrencyType(context),

                  // number 数字相关ui
                  // if (isNumber || isPercentage || isCurrency)
                  _buildNumberBody(context),

                  // if (isDate)
                  _buildDateBody(context),

                  // // 默认值（输入框）
                  // if (isText || isNumber || isPercentage || isCurrency)
                  ..._buildDefaultValue(context),

                  // // 发布是否必填
                  // if (isText || isNumber || isPercentage || isCurrency)
                  ..._buildSaveSubmitCheck(context),

                  // if (isSingleSelect || isMultipleSelect)
                  ..._buildOption(context),

                  // if (isTelephone)
                  _buildTelephone(context),

                  //// 复选框默认选中
                  _buildCheckbox(context),

                  // 字段/列描述
                  Selector<AppTableStateManage, bool>(
                    selector: (context, provider) => provider.showDescInput,
                    builder: (context, data, child) {
                      var _showDescInput = context.read<AppTableStateManage>().showDescInput;
                      return Visibility(
                        visible: _showDescInput,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: fieldSpace),
                            Container(
                              child: Text(
                                '字段/列描述',
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(height: fieldInputSpace),
                            AppBasicTextField(
                              // controller: _columnDescController,
                              initialValue: columnModel.type.columnDesc,
                              onChanged: (value) {
                                columnModel.type.columnDesc = value;
                              },
                              decoration: InputDecoration(
                                border: outlineInputBorder,
                                contentPadding: EdgeInsets.symmetric(horizontal: 10),
                                hintText: '请输入字段/列描述',
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // footer 部分
          Container(
            padding: EdgeInsets.only(top: contentBorderRadius),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey, width: 1)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () {
                      mgmt.toggleShowDescInput();
                    },
                    child: Row(
                      children: [
                        Icon(Icons.add),
                        Text(
                          '字段/列描述',
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 13,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                Spacer(),

                ElevatedButton(
                  child: Text('关闭'),
                  onPressed: () {
                    if (widget.onClose != null) {
                      widget.onClose!();
                    }
                  },
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  child: Text('保存'),
                  onPressed: () {
                    if (widget.onSave != null) {
                      // print(
                      //   'columnModel===${columnModel.type.defaultValue} ${columnModel.type.defaultValue is List}',
                      // );

                      widget.onSave!(columnModel);

                      // print('列名称======${columnModel.text}');

                      // _printMsg();
                    }
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// （Number || Percentage || currency） 对应的 ui
  Widget _buildNumberBody(BuildContext context) {
    var currentColumnTypeId = context.select<AppTableStateManage, ColumnTypeEnum>(
      (state) => state.currentColumnTypeId,
    );
    // var selectPanelWidth = contentWidth - (contentPadding * 2);
    var isCurrency = context.select<AppTableStateManage, bool>((state) => state.isCurrency);

    var isPercentage = context.select<AppTableStateManage, bool>((state) => state.isPercentage);

    var isNumber = context.select<AppTableStateManage, bool>((state) => state.isNumber);

    if (!(isCurrency || isPercentage || isNumber)) {
      return SizedBox.shrink();
    }

    var isShowPercentiles = context.select<AppTableStateManage, bool>(
      (state) => state.isShowPercentiles,
    );

    var precision = context.select<AppTableStateManage, int>((state) => state.precision);

    var numberTypes = context.select<AppTableStateManage, List<GeneralOption<int>>>(
      (state) => state.numberTypes,
    );

    // 数据精度
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '数据精度',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        //数据精度下拉选项
        AppSelect<int>(
          key: const ValueKey('select_precision'),
          options: numberTypes,
          value: precision,
          onChanged: (item) {
            context.read<AppTableStateManage>().changePrecision(item);
          },
        ),

        if (isNumber) SizedBox(height: contentBorderRadius),

        // 显示千分位
        if (isNumber)
          Container(
            height: 32,
            alignment: Alignment.centerLeft,
            child: Row(
              children: [
                Selector<AppTableStateManage, bool>(
                  selector: (context, provider) => provider.isShowPercentiles,
                  builder:
                      (context, showDescInput, child) => Checkbox(
                        value: showDescInput,
                        onChanged: (value) {
                          // columnModelType!.isShowPercentiles = value!;
                          context.read<AppTableStateManage>().setShowPercentiles(value!);

                          // _initNumberTypes(
                          //   isShowPer: value!,
                          //   isCurrency: isCurrency,
                          //   isPercentage: isPercentage,
                          //   isNumber: isNumber,
                          // );
                        },
                      ),
                ),
                Text('显示千分位'),
              ],
            ),
          ),
        SizedBox(height: fieldSpace),
      ],
    );
  }

  Widget _buildCheckbox(BuildContext context) {
    var isCheckbox = context.select<AppTableStateManage, bool>((state) => state.isCheckbox);

    if (!isCheckbox) {
      return SizedBox.shrink();
    }

    var isDefaultChecked = context.select<AppTableStateManage, bool>(
      (state) => state.isDefaultChecked,
    );

    return Container(
      height: 32,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          Checkbox(
            value: isDefaultChecked,
            onChanged: (value) {
              context.read<AppTableStateManage>().setDefaultChecked(value!);
            },
          ),
          Text('默认勾选'),
        ],
      ),
    );
  }

  /// 日期列对应 UI
  Widget _buildDateBody(BuildContext context) {
    var isDate = context.select<AppTableStateManage, bool>((state) => state.isDate);

    if (!isDate) {
      return SizedBox.shrink();
    }

    // var columnModelType = columnModel.type as AppTableColumnTypeDate;
    // var dateType = columnModelType.dateType;

    var dateType = context.select<AppTableStateManage, int>((state) => state.dateType);

    var showWeek = context.select<AppTableStateManage, bool>((state) => state.showWeek);

    var showTime = context.select<AppTableStateManage, bool>((state) => state.showTime);

    var dateTypes = context.select<AppTableStateManage, List<GeneralOption<int>>>(
      (state) => state.dateTypes,
    );

    // _initDateTypes(showWeek: showWeek, showTime: showTime);

    print('dateType=${dateType} showWeek=${showWeek} showTime=${showTime}');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '日期格式',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        AppSelect<int>(
          key: const ValueKey('select_date'),
          options: dateTypes,
          value: dateType,
          onChanged: (item) {
            // dateType = item;
            // if (isDate) {
            //   columnModelType.dateType = dateType;
            // }
            // setState(() {});

            context.read<AppTableStateManage>().changeDateType(item);
          },
        ),

        SizedBox(height: contentBorderRadius),

        // 显示星期
        Container(
          height: 32,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Checkbox(
                value: showWeek,
                onChanged: (value) {
                  context.read<AppTableStateManage>().changeShowWeek(value!);

                  // setState(() {
                  //   columnModelType.showWeek = value!;
                  //   _initDateTypes(
                  //     showWeek: columnModelType.showWeek,
                  //     showTime: columnModelType.showTime,
                  //   );
                  // });
                },
              ),
              Text('显示星期'),
            ],
          ),
        ),
        SizedBox(height: contentBorderRadius),

        // 显示时间
        Container(
          height: 32,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Checkbox(
                value: showTime, //columnModelType.showTime,

                onChanged: (value) {
                  context.read<AppTableStateManage>().changeShowTime(value!);

                  // setState(() {
                  //   columnModelType.showTime = value!;
                  //   _initDateTypes(
                  //     showWeek: columnModelType.showWeek,
                  //     showTime: columnModelType.showTime,
                  //   );
                  //   ////
                  // });
                },
              ),
              Text('显示时间'),
            ],
          ),
        ),

        /// 选择框默认值
        SizedBox(height: selectPanelPadding),
        _buildDateDefaultValue(context),
      ],
    );
  }

  /// 选择框”默认值“——单选、多选对应的默认值
  Widget _buildDateDefaultValue(BuildContext context) {
    // var selectPanelWidth = contentWidth - (contentPadding * 2);

    var isDate = context.select<AppTableStateManage, bool>((state) => state.isDate);

    if (!isDate) {
      return SizedBox.shrink();
    }

    AppTableColumnTypeDate? columnModelType = columnModel.type as AppTableColumnTypeDate;

    /// 默认值类型（无、指定选项）
    // var dateType = columnModelType.dateType;

    var dateType = context.select<AppTableStateManage, int>((state) => state.dateType);

    // var defaultValueType = columnModelType.defaultValueType;
    var defaultValueType = context.select<AppTableStateManage, int>(
      (state) => state.defaultValueType,
    );

    // var defaultDateValue = columnModelType.defaultValue;
    // var showWeek = columnModelType.showWeek;

    var showWeek = context.select<AppTableStateManage, bool>((state) => state.showWeek);

    // var showTime = columnModelType.showTime;
    var showTime = context.select<AppTableStateManage, bool>((state) => state.showTime);

    var _defaultValue = (columnModel.type as AppTableColumnTypeDate).defaultValue;
    if (_defaultValue == null || _defaultValue == '') {
      _defaultValue = null;
    }

    String _format =
        AppTableCommon.getDateFormatOption(dateType).text +
        (showWeek ? ' EEEE' : '') +
        (showTime ? ' HH:mm' : '');

    // 单选多选的 默认值 类型（无、指定选项）
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '默认值',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        Row(
          children: [
            // Text(dateType.toString()),
            // Text('-'),
            // Text(defaultValueType.toString()),
            // Text('-'),
            Expanded(
              child: AppSelect<int>(
                key: const ValueKey('select_date_type'),
                options: defaultDateTypes,
                value: defaultValueType,
                onChanged: (item) {
                  // defaultValueType = item;

                  context.read<AppTableStateManage>().chagneDefaultValueType(item);

                  // if (columnModelType != null) {
                  //   columnModelType.defaultValueType = defaultValueType;
                  //   columnModelType.defaultValue = null;
                  //   setState(() {});
                  // }

                  _printMsg();
                },
              ),
            ),
            SizedBox(width: 5),
            Expanded(
              child: Container(
                child:
                    defaultValueType == 2
                        ? AppDate(
                          format: _format,
                          initialValue: _defaultValue,
                          onChanged: (value) {
                            print("value: $value");
                            (columnModel.type as AppTableColumnTypeDate).defaultValue = value;
                            // setState(() {});
                          },
                        )
                        // ? AppSelect<String>(
                        //   key: const ValueKey('select_3'),
                        //   options: [],
                        //   value: selectDefaultValue,
                        //   // isMultiple: isMultipleSelect,
                        //   subType:
                        //       columnModel.typeCode == ColumnTypeEnum.singleSelect
                        //           ? SelectEnum.single
                        //           : SelectEnum.multiple,
                        //   onChanged: (item) {
                        //     selectDefaultValue = item;
                        //     if ((isSingleSelect || isMultipleSelect) &&
                        //         columnModelType != null) {
                        //       columnModelType.defaultValue = selectDefaultValue;
                        //       // } else if (isPercentage) {
                        //       //   (columnModel.type
                        //       //           as AppTableColumnTypePercentage)
                        //       //       .precision = prec;
                        //     }
                        //     _printMsg();
                        //   },
                        // )
                        : const SizedBox.shrink(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 输入框“默认值”
  List<Widget> _buildDefaultValue(BuildContext context) {
    var isText = context.select<AppTableStateManage, bool>((state) => state.isText);

    var isCurrency = context.select<AppTableStateManage, bool>((state) => state.isCurrency);

    var isPercentage = context.select<AppTableStateManage, bool>((state) => state.isPercentage);

    var isNumber = context.select<AppTableStateManage, bool>((state) => state.isNumber);

    if (!(isText || isNumber || isPercentage || isCurrency)) {
      return [SizedBox.shrink()];
    }

    List<Widget> result = [];

    dynamic columnModelType = null;

    //精度
    int precision = context.select<AppTableStateManage, int>(
      (state) => state.precision,
    ); // 精度（保留小数位数）
    bool negative = true; // 是否允许为负数
    bool isShowPercentiles = context.select<AppTableStateManage, bool>(
      (state) => state.isShowPercentiles,
    ); //是否显示千分位
    bool isRetainDecimal = true; // 是否强制保留小数（不足补0）
    String prefix = ''; //前缀

    var currencyType = context.select<AppTableStateManage, int>((state) => state.currencyType);

    if (isText) {
      columnModelType = columnModel.type as AppTableColumnTypeText;
    } else if (isNumber) {
      columnModelType = columnModel.type as AppTableColumnTypeNumber;
      negative = columnModelType.negative;
      // isShowPercentiles = columnModelType.isShowPercentiles;
      // precision = columnModelType.precision;
    } else if (isPercentage) {
      columnModelType = columnModel.type as AppTableColumnTypePercentage;
      // precision = columnModelType.precision;
      // isShowPercentiles = columnModelType.isShowPercentiles;
    } else if (isCurrency) {
      columnModelType = columnModel.type as AppTableColumnTypeCurrency;
      // precision = columnModelType.precision;
      var typeOption = AppTableCommon.getCurrencyType(currencyType);
      if (typeOption != null) {
        prefix = typeOption.symbol ?? '';
      }
      // isShowPercentiles = columnModelType.isShowPercentiles;
    }

    if (columnModelType == null) {
      return result;
    }

    result.add(
      Container(
        child: Text(
          '默认值',
          style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
        ),
      ),
    );
    result.add(SizedBox(height: fieldInputSpace));

    if (isText) {
      result.add(
        AppBasicTextField(
          // controller: _defaultValueController,
          initialValue: columnModel.type.defaultValue,
          onChanged: (value) {
            // columnModel.type.defaultValue = value;

            columnModelType.defaultValue = value;

            // setState(() {});
          },
          decoration: InputDecoration(
            border: outlineInputBorder,
            contentPadding: EdgeInsets.symmetric(horizontal: 10),
            hintText: '',
          ),
        ),
      );
    } else {
      // result.add(Text(columnModel.type.defaultValue.toString()));
      // result.add(Text('精度：${precision.toString()}'));
      // result.add(Text('是否显示千分位：${isShowPercentiles.toString()}'));
      // result.add(Text('是否补0：${isRetainDecimal.toString()}'));

      // print('精度：${precision} isShowPercentiles=${isShowPercentiles}');

      result.add(
        AppNumber(
          initialValue: columnModelType.defaultValue,
          precision: precision,
          isShowPercentiles: isShowPercentiles,
          isRetainDecimal: isRetainDecimal,
          negative: negative,
          prefix: isCurrency ? prefix : '',
          suffix: isPercentage ? '%' : '',
          onChanged: (value) {
            // columnModel.type.defaultValue = value;

            columnModelType.defaultValue = value;

            // setState(() {});
          },
        ),
      );
    }

    result.add(SizedBox(height: contentBorderRadius));

    return result;
  }

  /// 保存提交复选框
  List<Widget> _buildSaveSubmitCheck(BuildContext context) {
    var isText = context.select<AppTableStateManage, bool>((state) => state.isText);

    var isCurrency = context.select<AppTableStateManage, bool>((state) => state.isCurrency);

    var isPercentage = context.select<AppTableStateManage, bool>((state) => state.isPercentage);

    var isNumber = context.select<AppTableStateManage, bool>((state) => state.isNumber);

    if (!(isText || isNumber || isPercentage || isCurrency)) {
      return [SizedBox.shrink()];
    }

    var isSaveRequired = context.select<AppTableStateManage, bool>((state) => state.isSaveRequired);

    var isSubmitRequired = context.select<AppTableStateManage, bool>(
      (state) => state.isSubmitRequired,
    );

    List<Widget> result = [];
    // 设置为提交审批必填项

    dynamic columnModelType = null;

    if (isText) {
      columnModelType = columnModel.type as AppTableColumnTypeText;
    } else if (isNumber) {
      columnModelType = columnModel.type as AppTableColumnTypeNumber;
    } else if (isPercentage) {
      columnModelType = columnModel.type as AppTableColumnTypePercentage;
    } else if (isCurrency) {
      columnModelType = columnModel.type as AppTableColumnTypeCurrency;
    }

    if (columnModelType == null) {
      return result;
    }

    result.add(
      Container(
        height: 32,
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Checkbox(
              value: isSaveRequired,
              onChanged: (value) {
                context.read<AppTableStateManage>().setSaveRequired(value!);

                // columnModelType.isSaveRequired = value!;
                // setState(() {
                // });
              },
            ),
            Text('暂存是否必填'),
          ],
        ),
      ),
    );

    result.add(SizedBox(height: fieldInputSpace));

    result.add(
      Container(
        height: 32,
        alignment: Alignment.centerLeft,

        child: Row(
          children: [
            Checkbox(
              value: isSubmitRequired,
              onChanged: (value) {
                context.read<AppTableStateManage>().setSubmitRequired(value!);

                // columnModelType.isSubmitRequired = value!;
                // setState(() {
                // });
              },
            ),
            Text('设置为提交审批必填项'),
          ],
        ),
      ),
    );

    return result;
  }

  /// 构建“选项内容”组件
  List<Widget> _buildOption(BuildContext context) {
    var isSingleSelect = context.select<AppTableStateManage, bool>((state) => state.isSingleSelect);

    var isMultipleSelect = context.select<AppTableStateManage, bool>(
      (state) => state.isMultipleSelect,
    );

    if (!(isSingleSelect || isMultipleSelect)) {
      return [SizedBox.shrink()];
    }

    var options = context.select<AppTableStateManage, List<GeneralOption>>(
      (state) => state.options,
    );

    var optionsChangeFlag = context.select<AppTableStateManage, int>(
      (state) => state.optionsChangeFlag,
    );

    List<Widget> result = [];
    dynamic columnModelType = null;

    if (isSingleSelect || isMultipleSelect) {
      columnModelType = columnModel.type as AppTableColumnTypeSingleSelect;
    }

    if (columnModelType == null) {
      return result;
    }

    // double optionPanelHeight = 0;
    // optionPanelHeight = (inputHeight + 10) * columnModelType.options.length;
    // optionPanelHeight = optionPanelHeight.clamp(0, 200);

    result.add(
      Container(
        child: Text(
          '选项类容',
          style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
        ),
      ),
    );

    result.add(SizedBox(height: fieldInputSpace));

    result.add(
      Container(
        child: Column(
          children: [
            Row(
              children: [
                TextButton(
                  onPressed: () {
                    // _addOption(columnModelType);

                    context.read<AppTableStateManage>().addOption();
                  },
                  child: Text('添加选项'),
                ),
                TextButton(onPressed: () {}, child: Text('批量添加')),
              ],
            ),
            Container(
              constraints: BoxConstraints(
                maxHeight: 200, // 最大高度为200
              ),

              // height: optionPanelHeight,
              child: ReorderableListView(
                shrinkWrap: true, // 设置为 true 以确保列表适应内容高度
                buildDefaultDragHandles: false,
                children: [
                  for (int i = 0; i < options.length; i++)
                    Row(
                      key: ValueKey(options[i].id),
                      children: [
                        ReorderableDragStartListener(index: i, child: Icon(Icons.drag_indicator)),
                        SizedBox(width: 8),
                        // Text(columnModelType.options[i].id),
                        // Expanded(child: Text(columnModelType.options[i].text)),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 5),
                            child: AppBasicTextField(
                              // controller:
                              //     _optionsEditingCtrl[columnModelType
                              //         .options[i]
                              //         .id],
                              initialValue: options[i].text,
                              onChanged: (value) {
                                options[i].text = value;
                                // setState(() {
                                // });
                              },

                              decoration: InputDecoration(
                                border: outlineInputBorder,
                                contentPadding: EdgeInsets.symmetric(horizontal: 10),
                                hintText: '请输入字段名称',
                              ),
                            ),
                          ),
                        ),
                        GestureDetector(
                          child: Icon(Icons.delete),
                          onTap: () {
                            // _delOption(
                            //   columnModelType,
                            //   columnModelType.options[i].id,
                            // );

                            context.read<AppTableStateManage>().removeOption(options[i]);
                          },
                        ),
                      ],
                    ),

                  // for (int i = 0; i < columnModelType.options.length; i++)
                  //   ListTile(
                  //     key: ValueKey(columnModelType.options[i].id), // 必须有唯一key
                  //     title: Text(columnModelType.options[i].text),
                  //     leading: ReorderableDragStartListener(
                  //       index: i,
                  //       child: Icon(Icons.drag_indicator),
                  //     ),
                  //     // Icon(Icons.drag_handle),
                  //   ),
                ],
                onReorder: (int oldIndex, int newIndex) {
                  context.read<AppTableStateManage>().orderOption(oldIndex, newIndex);

                  // if (newIndex > oldIndex) newIndex -= 1;
                  // final item = columnModelType.options.removeAt(oldIndex);
                  // columnModelType.options.insert(newIndex, item);
                },
              ),
            ),
          ],
        ),
      ),
    );

    /// 选择框默认值
    result.add(SizedBox(height: selectPanelPadding));
    result.add(_buildSelectDefaultValue(context));

    return result;
  }

  /// 选择框”默认值“——单选、多选对应的默认值
  Widget _buildSelectDefaultValue(BuildContext context) {
    // var selectPanelWidth = contentWidth - (contentPadding * 2);

    var isSingleSelect = context.select<AppTableStateManage, bool>((state) => state.isSingleSelect);

    var isMultipleSelect = context.select<AppTableStateManage, bool>(
      (state) => state.isMultipleSelect,
    );

    if (!(isSingleSelect || isMultipleSelect)) {
      return SizedBox.shrink();
    }

    var options = context.select<AppTableStateManage, List<GeneralOption<String>>>(
      (state) => state.options,
    );

    AppTableColumnTypeSingleSelect? columnModelType = null;

    /// 默认值类型（无、指定选项）
    // var defaultValueType =
    //     (columnModel.type as AppTableColumnTypeSingleSelect).defaultValueType;
    var defaultValueType = context.select<AppTableStateManage, int>(
      (state) => state.selDefaultValueType,
    );

    /// 默认值选中
    var selectDefaultValue = (columnModel.type as AppTableColumnTypeSingleSelect).defaultValue;

    /// 如果时多选，默认选中应该时一个集合
    if (isMultipleSelect) {
      if (selectDefaultValue is! Iterable) {
        selectDefaultValue = selectDefaultValue ?? [];
      }
    }

    /// 默认值备选项
    List<GeneralOption<String>> defaultOptions = [];

    if (isSingleSelect || isMultipleSelect) {
      columnModelType = columnModel.type as AppTableColumnTypeSingleSelect;
      defaultOptions = options;
    }

    // 单选多选的 默认值 类型（无、指定选项）
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '默认值',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        Row(
          children: [
            Expanded(
              child: AppSelect(
                key: const ValueKey('select_4'),
                options: defaultValueTypes,
                value: defaultValueType,
                onChanged: (item) {
                  // defaultValueType = item;

                  context.read<AppTableStateManage>().changeSelDefaultValueType(item);

                  print('selectDefaultValue====${selectDefaultValue}');

                  // if ((isSingleSelect || isMultipleSelect) &&
                  //     columnModelType != null) {
                  //   columnModelType.defaultValueType = defaultValueType;
                  //   columnModelType.defaultValue = null;

                  //   // (columnModel.type as AppTableColumnTypeSingleSelect)
                  //   //     .defaultValueType = defaultValueType;

                  //   // (columnModel.type as AppTableColumnTypeSingleSelect)
                  //   //     .defaultValue = null;

                  //   selectDefaultValue = null;

                  //   // setState(() {});

                  //   // } else if (isPercentage) {
                  //   //   (columnModel.type
                  //   //           as AppTableColumnTypePercentage)
                  //   //       .precision = prec;
                  // }

                  _printMsg();
                },
              ),
            ),
            SizedBox(width: 5),
            Expanded(
              child: Container(
                child:
                    defaultValueType == 2
                        ? AppSelect<String>(
                          key: const ValueKey('select_3'),
                          options: defaultOptions,
                          value: selectDefaultValue,
                          // isMultiple: isMultipleSelect,
                          subType:
                              columnModel.type.typeCode == ColumnTypeEnum.singleSelect
                                  ? SelectEnum.single
                                  : SelectEnum.multiple,
                          onChanged: (item) {
                            // print(
                            //   'object==========${item} ${item is String} ${item is Array} ${item is List}',
                            // );
                            selectDefaultValue = item;
                            if ((isSingleSelect || isMultipleSelect) && columnModelType != null) {
                              if (isSingleSelect) {
                                print(
                                  'columnModel.typeCode===${columnModel.type.typeCode} 单选 selectDefaultValue==${selectDefaultValue}',
                                );

                                columnModelType.defaultValue = selectDefaultValue as String;
                              } else {
                                columnModelType.defaultValue = selectDefaultValue as List<String>;

                                print('多选 selectDefaultValue==${selectDefaultValue}');

                                // print(
                                //   'object======${selectDefaultValue} ${selectDefaultValue is List}',
                                // );
                              }
                              // } else if (isPercentage) {
                              //   (columnModel.type
                              //           as AppTableColumnTypePercentage)
                              //       .precision = prec;
                            }

                            _printMsg();
                          },
                        )
                        : const SizedBox.shrink(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 电脑号码类型下拉
  Widget _buildTelephone(BuildContext context) {
    var isTelephone = context.select<AppTableStateManage, bool>((state) => state.isTelephone);

    if (!isTelephone) {
      return const SizedBox.shrink();
    }

    AppTableColumnTypeTelephone? columnModelType = columnModel.type as AppTableColumnTypeTelephone;

    /// 电话类型
    var telephoneType = columnModelType.telephoneType;

    // 数据格式（电话号码）
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '数据格式',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        AppSelect<TelephoneEnum>(
          key: const ValueKey('select_5'),
          options: telephoneTypes,
          value: telephoneType,
          onChanged: (item) {
            telephoneType = item;
            if (isTelephone && columnModelType != null) {
              columnModelType.telephoneType = telephoneType;
              columnModelType.defaultValue = null;
              // setState(() {});
            }

            _printMsg();
          },
        ),
      ],
    );
  }

  Widget _buildCurrencyType(BuildContext context) {
    // final columnStatus = context.select<AppTableStateManage, String>(
    //   (state) => state.columnStatus,
    // );

    // print('_buildCurrencyType1111');r
    var isCurrency = context.select<AppTableStateManage, bool>((state) => state.isCurrency);

    if (!isCurrency) {
      return const SizedBox.shrink();
    }

    /// 货币类型
    var currencyType = context.select<AppTableStateManage, int>((state) => state.currencyType);

    // print('_buildCurrencyType2222---${isCurrency}');
    // // columnModel.typeCode == ColumnTypeEnum.currency
    // if (isCurrency) {
    //   // AppTableColumnTypeCurrency? columnModelType =
    //   //     columnModel.type as AppTableColumnTypeCurrency;

    //   // var currencyType = columnModelType.currencyType;

    //   // 数据格式（电话号码）
    // }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // SizedBox(height: fieldSpace),
        Container(
          child: Text(
            '货币类型',
            style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
          ),
        ),
        SizedBox(height: fieldInputSpace),
        AppSelect<int>(
          key: const ValueKey('select_currency_type'),
          options: AppTableCommon.currencyTypes,
          value: currencyType,
          onChanged: (item) {
            // currencyType = item;

            // if (isCurrency) {
            //   columnModelType.currencyType = currencyType;
            //   columnModelType.defaultValue = null;
            // }

            context.read<AppTableStateManage>().changeCurrencyType(item);

            // setState(() {});

            _printMsg();
          },
        ),
        SizedBox(height: fieldSpace),
      ],
    );
  }

  // /// 选项内容相关
  // void _addOption(dynamic columnModelType) {
  //   var optionId = _uuid.v4();
  //   var option = GeneralOption(id: optionId, text: '');

  //   // _optionsEditingCtrl[optionId] = TextEditingController(
  //   //   text: columnModel.text,
  //   // );

  //   columnModelType.options.add(option);
  //   // setState(() {
  //   // });
  // }

  // void _delOption(dynamic columnModelType, String optionId) {
  //   if (optionId != null
  //   // &&
  //   //     _optionsEditingCtrl.containsKey(optionId) &&
  //   //     _optionsEditingCtrl[optionId] != null
  //   ) {
  //     // _optionsEditingCtrl[optionId]!.dispose();
  //     // _optionsEditingCtrl.remove(optionId);

  //     columnModelType.options.removeWhere((element) => element.id == optionId);
  //   }
  //   // setState(() {});
  // }

  /// 切换字段类型
  /// [target] 目标类型
  void _changeColumnType(AppTableStateManage mgmt, dynamic target) {
    //GeneralOption
    if (columnModel.type.typeCode != target) {
      mgmt.changeColumnType(target);

      // var isCurrency = target == ColumnTypeEnum.currency;

      // var isPercentage = target == ColumnTypeEnum.percentage;

      // var isNumber = target == ColumnTypeEnum.number;

      // var isDate = target == ColumnTypeEnum.date;

      // 重新赋值对应的类型（类型改变后，某些集合需要初始化）
      // if (isNumber || isPercentage || isCurrency) {
      //   _initNumberTypes(
      //     isCurrency: isCurrency,
      //     isPercentage: isPercentage,
      //     isNumber: isNumber,
      //   );
      // }

      // if (isDate) {
      //   _initDateTypes();
      // }

      _printMsg();
    }
  }

  /// 切换类型，刷新表单数据（保留列id、标题）
  void _refreshForm() {
    var oldId = columnModel.field;
    var oldText = columnModel.text;
    columnModel = AppTableGeneralHelper.newColumn(type: columnModel.type.typeCode);
    columnModel.field = oldId;
    columnModel.text = oldText;
    _clearForm();
  }

  void _clearForm() {
    // _nameController.clear();
    // _defaultValueController.clear();
    // _columnDescController.clear();

    // if (_optionsEditingCtrl.isNotEmpty) {
    //   _optionsEditingCtrl.forEach((key, value) {
    //     value.clear();
    //   });
    // }
  }

  // /// [isShowPer] 是否显示千分位
  // void _initNumberTypes({
  //   bool isShowPer = false,
  //   bool isCurrency = false,
  //   bool isPercentage = false,
  //   bool isNumber = false,
  // }) {
  //   // var isNumber = columnModel.typeCode == ColumnTypeEnum.number;
  //   // var isPercentage = columnModel.typeCode == ColumnTypeEnum.percentage;

  //   int numFlag = 1234;
  //   if (isPercentage) {
  //     numFlag = 12;
  //   } else if (isCurrency) {
  //     numFlag = 1;
  //   }

  //   numberTypes = List.generate(5, (index) {
  //     return GeneralOption<int>(
  //       id: index,
  //       text: AppTableGeneralHelper.getNumberFormatStr(
  //         numFlag,
  //         isShowPercentiles: isNumber ? isShowPer : false,
  //         precision: index,
  //         isRetainDecimal: true,
  //         suffix: isPercentage ? '%' : '',
  //       ),
  //     );
  //   });
  // }

  // void _initDateTypes({bool showWeek = false, bool showTime = false}) {
  //   var now = DateTime.now();

  //   // 使用 List.generate 方法
  //   dateTypes = List.generate(dateFormats.length, (index) {
  //     var format =
  //         AppTableCommon.getDateFormatOption(index + 1).text +
  //         (showWeek ? ' EEEE' : '') +
  //         (showTime ? ' HH:mm' : '');
  //     var text = DateFormat(format, 'zh').format(now);
  //     return GeneralOption<int>(id: index + 1, text: text);
  //   });
  // }

  void _printMsg() {
    // var msg =
    //     'text=${columnModel.text}；defaultValue=${columnModel.type.defaultValue}；columnDesc=${columnModel.type.columnDesc}；';

    // if (isText || isNumber || isPercentage) {
    //   dynamic columnType = null;
    //   if (isText) {
    //     columnType = columnModel.type as AppTableColumnTypeText;
    //   } else if (isNumber) {
    //     columnType = columnModel.type as AppTableColumnTypeNumber;
    //   } else if (isPercentage) {
    //     columnType = columnModel.type as AppTableColumnTypePercentage;
    //   }

    //   if (columnType != null) {
    //     msg +=
    //         'isSaveRequired=${columnType.isSaveRequired}；isSubmitRequired=${columnType.isSubmitRequired}';
    //   }
    // }

    // if (isNumber || isPercentage) {
    //   var prec = 0;
    //   if (isNumber) {
    //     prec = (columnModel.type as AppTableColumnTypeNumber).precision;
    //   } else if (isPercentage) {
    //     prec = (columnModel.type as AppTableColumnTypePercentage).precision;
    //   }
    //   msg += '——————precision=${prec}；';
    // }

    // if (isNumber) {
    //   var columnModelType = columnModel.type as AppTableColumnTypeNumber;
    //   msg += 'isShowPercentiles=${columnModelType.isShowPercentiles}；';
    // }

    // if (isSingleSelect || isMultipleSelect) {
    //   var columnModelType = columnModel.type as AppTableColumnTypeSingleSelect;
    //   for (var i = 0; i < columnModelType.options.length; i++) {
    //     msg += '${columnModelType.options[i].text}；';
    //   }

    //   msg += 'defaultValueType=${columnModelType.defaultValueType}；';
    // }

    // print(msg);
  }

  @override
  void dispose() {
    // _nameController.dispose();
    // _columnDescController.dispose();
    // _defaultValueController.dispose();

    // if (_optionsEditingCtrl.isNotEmpty) {
    //   _optionsEditingCtrl.forEach((key, value) {
    //     value.dispose();
    //   });
    // }

    super.dispose();
  }
}
