// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_date.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeDate _$AppTableColumnTypeDateFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeDate(
        defaultValue: json['DefaultValue'],
        columnDesc: json['ColumnDesc'] as String? ?? '',
        dateType: (json['DateType'] as num?)?.toInt() ?? 1,
        showWeek: json['ShowWeek'] as bool? ?? false,
        showTime: json['ShowTime'] as bool? ?? false,
        defaultValueType: (json['DefaultValueType'] as num?)?.toInt() ?? 1,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.date;

Map<String, dynamic> _$AppTableColumnTypeDateToJson(
  AppTableColumnTypeDate instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'DateType': instance.dateType,
  'ShowWeek': instance.showWeek,
  'ShowTime': instance.showTime,
  'DefaultValueType': instance.defaultValueType,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
