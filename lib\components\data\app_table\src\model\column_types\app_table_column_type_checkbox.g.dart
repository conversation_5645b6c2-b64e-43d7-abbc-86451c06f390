// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_checkbox.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeCheckbox _$AppTableColumnTypeCheckboxFromJson(
  Map<String, dynamic> json,
) => AppTableColumnTypeCheckbox(
    defaultValue: json['DefaultValue'] ?? false,
    columnDesc: json['columnDesc'] as String? ?? '',
    isSaveRequired: json['IsSaveRequired'] as bool? ?? false,
    isSubmitRequired: json['IsSubmitRequired'] as bool? ?? false,
  )
  ..typeCode =
      $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
      ColumnTypeEnum.checkbox;

Map<String, dynamic> _$AppTableColumnTypeCheckboxToJson(
  AppTableColumnTypeCheckbox instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
