import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_request_field.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_text.g.dart';

/// 文本（text）、超链接（hyperlink）、父记录（parent_record）
@JsonSerializable()
class AppTableColumnTypeText
    with AppTableColumnTypeDefaultMixin, AppTableColumnTypeWithRequestField
    implements AppTableColumnType {
  dynamic _defaultValue;

  @JsonKey(name: 'DefaultValue')
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  @Json<PERSON>ey(name: 'IsSaveRequired', defaultValue: false)
  @override
  bool isSaveRequired = false;

  @JsonKey(name: 'IsSubmitRequired', defaultValue: false)
  @override
  bool isSubmitRequired = false;

  String _columnDesc;
  @JsonKey(name: 'ColumnDesc', defaultValue: '')
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @JsonKey(name: 'TypeCode', defaultValue: ColumnTypeEnum.text)
  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.text;

  AppTableColumnTypeText({
    dynamic defaultValue,
    String columnDesc = '',
    this.isSaveRequired = false,
    this.isSubmitRequired = false,
    this.typeCode = ColumnTypeEnum.text,
  }) : _defaultValue = defaultValue,
       _columnDesc = columnDesc;

  @override
  bool isValid(dynamic value) {
    return value is String || value is num;
  }

  @override
  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(a, b, () => a.toString().compareTo(b.toString()));
  }

  @override
  dynamic markCompareValue(dynamic value) {
    return value.toString();
  }

  factory AppTableColumnTypeText.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeTextFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeTextToJson(this);
}
