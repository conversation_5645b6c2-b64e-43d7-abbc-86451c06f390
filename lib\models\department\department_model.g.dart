// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'department_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DepartmentModel _$DepartmentModelFromJson(Map<String, dynamic> json) =>
    DepartmentModel(
      parentIdList:
          (json['ParentIdList'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      parentList:
          (json['ParentList'] as List<dynamic>?)
              ?.map((e) => DepartmentModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      departmentHeadIdList:
          (json['DepartmentHeadIdList'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      departmentHeadList:
          (json['DepartmentHeadList'] as List<dynamic>?)
              ?.map((e) => Employee.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      id: json['Id'] as String?,
      departmentName: json['DepartmentName'] as String? ?? '',
      parentId: json['ParentId'] as String?,
      parentName: json['ParentName'] as String? ?? '',
      secondarySuperiorIds: json['SecondarySuperiorIds'] as String?,
      departmentHrbpId: json['DepartmentHRBPId'] as String?,
      departmentHrbpName: json['DepartmentHRBPName'] as String?,
      description: json['Description'] as String? ?? '',
    );

Map<String, dynamic> _$DepartmentModelToJson(DepartmentModel instance) =>
    <String, dynamic>{
      'ParentIdList': instance.parentIdList,
      'ParentList': instance.parentList,
      'Id': instance.id,
      'DepartmentName': instance.departmentName,
      'ParentId': instance.parentId,
      'ParentName': instance.parentName,
      'SecondarySuperiorIds': instance.secondarySuperiorIds,
      'DepartmentHeadIdList': instance.departmentHeadIdList,
      'DepartmentHeadList': instance.departmentHeadList,
      'DepartmentHRBPId': instance.departmentHrbpId,
      'DepartmentHRBPName': instance.departmentHrbpName,
      'Description': instance.description,
    };
