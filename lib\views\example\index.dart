import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';
import 'package:octasync_client/views/example/buttons_example_page.dart';
import 'package:octasync_client/views/example/table_demo.dart';
import 'package:octasync_client/views/example/table_tool.dart';
import 'package:octasync_client/views/example/tabs_example_page.dart';
import 'package:octasync_client/views/example/toggle_button_sample_page.dart';
import 'package:octasync_client/views/example/tooltip_example_page.dart';
import 'package:octasync_client/views/example/dropdown_example_page.dart';
import 'package:octasync_client/views/example/dialog_example_page.dart';
import 'package:octasync_client/views/example/loading_example_page.dart';
import 'package:octasync_client/views/example/popup_overlay_example_page.dart';
import 'package:octasync_client/views/example/select_example_page.dart';
import 'package:octasync_client/views/example/image_crop_example_page.dart';
import 'package:octasync_client/views/example/app_input_number_example.dart';
import 'package:octasync_client/views/example/tree_example_page.dart';
import 'package:octasync_client/views/example/form_field_example_page.dart';

/// 示例项定义
class ExampleItem {
  final String title;
  final IconData icon;
  final Widget page;

  const ExampleItem({required this.title, required this.icon, required this.page});
}

class ExamplePage extends StatefulWidget {
  const ExamplePage({super.key});

  @override
  State<ExamplePage> createState() => _ExamplePageState();
}

class _ExamplePageState extends State<ExamplePage> {
  // 当前选中的示例索引
  int _selectedIndex = 0;

  // 示例列表，将来可以在这里添加更多示例
  final List<ExampleItem> _examples = [
    ExampleItem(title: '按钮示例', icon: Icons.smart_button, page: const ButtonsExamplePage()),
    ExampleItem(title: 'Tooltip', icon: Icons.tips_and_updates, page: const TooltipExamplePage()),
    ExampleItem(
      title: '下拉菜单',
      icon: Icons.arrow_drop_down_circle,
      page: const DropdownExamplePage(),
    ),
    ExampleItem(
      title: 'toggle_btn',
      icon: Icons.arrow_drop_down_circle,
      page: const ToggleButtonSamplePage(),
    ),
    ExampleItem(title: '对话框', icon: Icons.chat, page: const DialogExamplePage()),
    ExampleItem(title: 'Tabs', icon: Icons.tab_sharp, page: const TabsExamplePage()),
    ExampleItem(title: '浮层', icon: Icons.layers, page: const PopupOverlayExamplePage()),
    ExampleItem(title: '选择器', icon: Icons.list, page: const SelectExamplePage()),
    ExampleItem(title: 'Loading', icon: Icons.hourglass_empty, page: const LoadingExamplePage()),
    ExampleItem(title: '图片裁剪', icon: Icons.crop, page: const ImageCropExamplePage()),
    ExampleItem(title: '数字输入框', icon: Icons.pin, page: const AppInputNumberExample()),
    ExampleItem(title: '表格组件', icon: Icons.table_view, page: const TableDemo()),
    ExampleItem(title: '表格工具', icon: Icons.table_view, page: const TableTool()),
    ExampleItem(title: '树组件', icon: Icons.account_tree, page: const TreeExamplePage()),
    ExampleItem(title: '表单字段', icon: Icons.article, page: const FormFieldExamplePage()),

    // 未来可以在这里添加更多示例项
  ];

  @override
  Widget build(BuildContext context) {
    double height = MediaQuery.of(context).size.height;
    double contentHeight = _examples.length * 68;
    height = height < contentHeight ? contentHeight : height;

    return Scaffold(
      appBar: AppBar(title: const Text('组件示例')),
      body: Row(
        children: [
          // 左侧导航栏
          SingleChildScrollView(
            child: Container(
              height: height,
              child: NavigationRail(
                selectedIndex: _selectedIndex,
                indicatorColor: AppColors.primary,
                onDestinationSelected: (int index) {
                  setState(() {
                    _selectedIndex = index;
                  });
                },
                labelType: NavigationRailLabelType.all,
                destinations:
                    _examples.map((example) {
                      return NavigationRailDestination(
                        icon: Icon(example.icon),
                        label: Text(example.title),
                      );
                    }).toList(),
              ),
            ),
          ),
          VerticalDivider(width: 1, color: AppColors.primary),
          // 右侧内容区域
          Expanded(child: _examples[_selectedIndex].page),
        ],
      ),
    );
  }
}
