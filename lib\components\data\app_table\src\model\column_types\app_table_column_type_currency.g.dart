// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_currency.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeCurrency _$AppTableColumnTypeCurrencyFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeCurrency(
        defaultValue: json['DefaultValue'],
        columnDesc: json['columnDesc'] as String? ?? '',
        negative: json['negative'] as bool,
        format: json['format'] as String,
        applyFormatOnInit: json['applyFormatOnInit'] as bool,
        allowFirstDot: json['allowFirstDot'] as bool,
        locale: json['locale'] as String?,
        currencyType: (json['currencyType'] as num?)?.toInt() ?? 1,
        precision: (json['precision'] as num?)?.toInt() ?? 0,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.currency;

Map<String, dynamic> _$AppTableColumnTypeCurrencyToJson(
  AppTableColumnTypeCurrency instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
  'negative': instance.negative,
  'allowFirstDot': instance.allowFirstDot,
  'locale': instance.locale,
  'format': instance.format,
  'applyFormatOnInit': instance.applyFormatOnInit,
  'currencyType': instance.currencyType,
  'precision': instance.precision,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
