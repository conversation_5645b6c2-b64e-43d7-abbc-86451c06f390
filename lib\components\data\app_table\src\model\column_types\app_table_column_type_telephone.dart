import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/telephone_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_request_field.dart';
// import 'package:octasync_client/components/data/app_table/model/app_table_column_type_has_format.dart';
// import 'package:octasync_client/components/data/app_table/model/app_table_column_type_with_number_format.dart';
// import 'package:intl/intl.dart' as intl;
import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_telephone.g.dart';

@JsonSerializable()
class AppTableColumnTypeTelephone
    with
        AppTableColumnTypeDefaultMixin //, AppTableColumnTypeWithNumberFormat
        ,
        AppTableColumnTypeWithRequestField
    implements
        AppTableColumnType //, AppTableColumnTypeHasFormat<String>
        {
  dynamic _defaultValue;

  @JsonKey(name: 'DefaultValue')
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  @JsonKey(name: 'IsSaveRequired', defaultValue: false)
  @override
  bool isSaveRequired = false;

  @JsonKey(name: 'IsSubmitRequired', defaultValue: false)
  @override
  bool isSubmitRequired = false;

  String _columnDesc;
  @JsonKey(name: 'ColumnDesc', defaultValue: '')
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @JsonKey(name: 'TypeCode', defaultValue: ColumnTypeEnum.telephone)
  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.telephone;
  // @override
  // final intl.NumberFormat numberFormat;

  // @override
  // final bool negative;

  // @override
  // final bool allowFirstDot;

  // @override
  // final String? locale;

  // @override
  // final String format;

  // @override
  // final bool applyFormatOnInit;

  /// 精度
  @JsonKey(name: 'TelephoneType', defaultValue: TelephoneEnum.mobile)
  TelephoneEnum telephoneType = TelephoneEnum.mobile;

  AppTableColumnTypeTelephone({
    dynamic defaultValue = '',
    String columnDesc = '',
    // required this.negative,
    // required this.format,
    // required this.applyFormatOnInit,
    // required this.allowFirstDot,
    // required this.locale,
    TelephoneEnum telephoneType = TelephoneEnum.mobile,
  }) : _defaultValue = defaultValue,
       //  numberFormat = intl.NumberFormat(format, locale),
       _columnDesc = columnDesc;
  //  decimalPoint = _getDecimalPoint(format);
  // static int _getDecimalPoint(String format) {
  //   final int dotIndex = format.indexOf('.');
  //   return dotIndex < 0 ? 0 : format.substring(dotIndex).length - 1;
  // }

  @override
  bool isValid(dynamic value) {
    return value is String || value is num;
  }

  @override
  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(a, b, () => a.toString().compareTo(b.toString()));
  }

  @override
  dynamic markCompareValue(dynamic value) {
    return value.toString();
  }

  factory AppTableColumnTypeTelephone.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeTelephoneFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeTelephoneToJson(this);
}
