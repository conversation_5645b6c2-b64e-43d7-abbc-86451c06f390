// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_single_select.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeSingleSelect _$AppTableColumnTypeSingleSelectFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeSingleSelect(
        defaultValue: json['DefaultValue'] ?? '',
        columnDesc: json['ColumnDesc'] as String? ?? '',
        subType:
            $enumDecodeNullable(_$SelectEnumEnumMap, json['SubType']) ??
            SelectEnum.single,
      )
      ..isSaveRequired = json['IsSaveRequired'] as bool? ?? false
      ..isSubmitRequired = json['IsSubmitRequired'] as bool? ?? false
      ..options = AppTableColumnTypeSingleSelect._optionsFromJson(
        json['Options'] as List?,
      )
      ..defaultValueType = (json['DefaultValueType'] as num?)?.toInt() ?? 1
      ..typeCode =
          $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
          ColumnTypeEnum.singleSelect;

Map<String, dynamic> _$AppTableColumnTypeSingleSelectToJson(
  AppTableColumnTypeSingleSelect instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'Options': AppTableColumnTypeSingleSelect._optionsToJson(instance.options),
  'DefaultValueType': instance.defaultValueType,
  'SubType': _$SelectEnumEnumMap[instance.subType]!,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$SelectEnumEnumMap = {
  SelectEnum.single: 'single',
  SelectEnum.multiple: 'multiple',
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
