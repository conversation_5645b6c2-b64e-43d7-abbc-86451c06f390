// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_text.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeText _$AppTableColumnTypeTextFromJson(
  Map<String, dynamic> json,
) => AppTableColumnTypeText(
  defaultValue: json['DefaultValue'],
  columnDesc: json['ColumnDesc'] as String? ?? '',
  isSaveRequired: json['IsSaveRequired'] as bool? ?? false,
  isSubmitRequired: json['IsSubmitRequired'] as bool? ?? false,
  typeCode:
      $enumDecodeNullable(_$ColumnTypeEnumEnumMap, json['TypeCode']) ??
      ColumnTypeEnum.text,
);

Map<String, dynamic> _$AppTableColumnTypeTextToJson(
  AppTableColumnTypeText instance,
) => <String, dynamic>{
  'DefaultValue': instance.defaultValue,
  'IsSaveRequired': instance.isSaveRequired,
  'IsSubmitRequired': instance.isSubmitRequired,
  'ColumnDesc': instance.columnDesc,
  'TypeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
