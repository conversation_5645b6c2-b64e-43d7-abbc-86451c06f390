import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_checkbox.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_hyperlink.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_parent_record.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_telephone.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_text.dart';

abstract interface class AppTableColumnType {
  @JsonKey(name: 'DefaultValue')
  dynamic get defaultValue;
  set defaultValue(dynamic value);

  @JsonKey(name: 'ColumnDesc', defaultValue: '')
  String get columnDesc;
  set columnDesc(String value);

  @JsonKey(name: 'TypeCode', defaultValue: ColumnTypeEnum.text)
  ColumnTypeEnum get typeCode;

  /// 设置一个 string 列.
  factory AppTableColumnType.text({dynamic defaultValue = '', String columnDesc = ''}) {
    return AppTableColumnTypeText(defaultValue: defaultValue, columnDesc: columnDesc);
  }

  /// 设置一个 number 列.
  /// [format]
  /// '#,###' (每三位数字使用逗号)
  /// '#,###.###' (允许 3 个小数位)
  ///
  /// [negative] 允许负数
  ///
  /// [applyFormatOnInit] 当编辑器加载时，它会将值重置为 [format]。
  ///
  /// [allowFirstDot] 接受负数时，开头允许有一个点。在 .- 符号使用一个按钮的设备上，此选项是必需的。
  ///
  /// [locale] 指定列的数字区域设置。如果未指定，则使用默认区域设置。
  ///
  factory AppTableColumnType.number({
    dynamic defaultValue,
    String columnDesc = '',
    bool negative = true,
    String format = '#.###',
    bool applyFormatOnInit = true,
    bool allowFirstDot = false,
    int precision = 0, // 精度
    bool isShowPercentiles = false, // 是否显示千分位
    String? locale,
  }) {
    return AppTableColumnTypeNumber(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      applyFormatOnInit: applyFormatOnInit,
      negative: negative,
      format: format,
      allowFirstDot: allowFirstDot,
      precision: precision,
      isShowPercentiles: isShowPercentiles,
      locale: locale,
    );
  }

  /// 日期
  factory AppTableColumnType.date({
    dynamic defaultValue,
    String columnDesc = '',
    int dateType = 1, // 日期格式
    bool showWeek = false, //显示星期
    bool showTime = false, // 显示时间
    int defaultValueType = 1, // 默认值类型（1：无；2：指定日期；3：添加此记录的日期）
    // String? locale,
  }) {
    return AppTableColumnTypeDate(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      dateType: dateType,
      showWeek: showWeek,
      showTime: showTime,
      defaultValueType: defaultValueType,
      // locale: locale,
    );
  }

  /// 百分比——参考number
  factory AppTableColumnType.percentage({
    dynamic defaultValue,
    String columnDesc = '',
    bool negative = true,
    String format = '#.###',
    bool applyFormatOnInit = true,
    bool allowFirstDot = false,
    String? locale,
  }) {
    return AppTableColumnTypePercentage(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      applyFormatOnInit: applyFormatOnInit,
      negative: negative,
      format: format,
      allowFirstDot: allowFirstDot,
      locale: locale,
    );
  }

  /// 勾选框
  factory AppTableColumnType.checkbox({dynamic defaultValue = false, String columnDesc = ''}) {
    return AppTableColumnTypeCheckbox(defaultValue: defaultValue, columnDesc: columnDesc);
  }

  /// 单选
  factory AppTableColumnType.singleSelect({dynamic defaultValue = '', String columnDesc = ''}) {
    return AppTableColumnTypeSingleSelect(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      // isMultiple: false,
      subType: SelectEnum.single,
    );
  }

  /// 多选
  factory AppTableColumnType.multipleSelect({dynamic defaultValue, String columnDesc = ''}) {
    return AppTableColumnTypeSingleSelect(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      // isMultiple: true,
      subType: SelectEnum.multiple,
    );
  }

  /// 超链接
  factory AppTableColumnType.hyperlink({dynamic defaultValue = '', String columnDesc = ''}) {
    return AppTableColumnTypeHyperlink(defaultValue: defaultValue, columnDesc: columnDesc);
  }

  /// 父记录
  factory AppTableColumnType.parentRecord({dynamic defaultValue = '', String columnDesc = ''}) {
    return AppTableColumnTypeParentRecord(defaultValue: defaultValue, columnDesc: columnDesc);
  }

  /// 电话号码
  factory AppTableColumnType.telephone({dynamic defaultValue = '', String columnDesc = ''}) {
    return AppTableColumnTypeTelephone(defaultValue: defaultValue, columnDesc: columnDesc);
  }

  /// 货币类型——参考number
  factory AppTableColumnType.currency({
    dynamic defaultValue,
    String columnDesc = '',
    bool negative = true,
    String format = '#.###',
    bool applyFormatOnInit = true,
    bool allowFirstDot = false,
    String? locale,
    int currencyType = 1,
  }) {
    return AppTableColumnTypeCurrency(
      defaultValue: defaultValue,
      columnDesc: columnDesc,
      applyFormatOnInit: applyFormatOnInit,
      negative: negative,
      format: format,
      allowFirstDot: allowFirstDot,
      locale: locale,
      currencyType: currencyType,
    );
  }

  bool isValid(dynamic value);

  int compare(dynamic a, dynamic b);

  dynamic markCompareValue(dynamic value);

  (bool, dynamic) filteredValue({dynamic newValue, dynamic oldValue});

  // 添加 toJson 方法到接口中
  Map<String, dynamic> toJson();
}

mixin AppTableColumnTypeDefaultMixin {
  (bool, dynamic) filteredValue({dynamic newValue, dynamic oldValue}) => (false, newValue);
}
