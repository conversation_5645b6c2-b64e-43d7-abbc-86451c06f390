import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/organization/member_department/create_member_dialog.dart';

/// 成员页
class MemberPage extends StatefulWidget {
  const MemberPage({super.key});

  @override
  State<MemberPage> createState() => _MemberPageState();
}

class _MemberPageState extends State<MemberPage> {
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  final GlobalKey<MemberDialogState> _memberDialogStateKey = GlobalKey<MemberDialogState>();

  // 当前选中的部门（高亮显示）
  DepartmentModel? _selectedDepartment;

  late AppTableStateManage _stateManage;
  List<AppTableColumn> get colsTemp => [
    AppTableColumn(
      text: '名称',
      field: 'Name',
      type: AppTableColumnType.text(),
      width: 120,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '工号',
      field: 'Number',
      type: AppTableColumnType.text(),
      width: 100,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '部门',
      field: 'DepartmentList',
      type: AppTableColumnType.text(),
      frozen: AppTableColumnFrozen.start,
      width: 150,
      resizable: true, // 允许调整列宽
      cellBuilder: (context, value, column, row) {
        if (value is List<dynamic>) {
          return SizedBox(
            width: column.width - 10,
            child: Padding(
              padding: const EdgeInsets.all(5),
              child: Wrap(
                direction: Axis.horizontal,
                alignment: WrapAlignment.center,
                spacing: 5.0,
                runSpacing: 5.0,
                children:
                    value
                        .map<Widget>(
                          (t) => AppTag(text: t['DepartmentName'], color: AppColors.info),
                        )
                        .toList(),
              ),
            ),
          );
        }
        return SizedBox();
      },
    ),
    AppTableColumn(
      text: '手机号码',
      field: 'PhoneNumber',
      type: AppTableColumnType.text(),
      width: 100,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '工作邮箱',
      field: 'Email',
      type: AppTableColumnType.text(),
      width: 100,
      resizable: true, // 允许调整列宽
      frozen: AppTableColumnFrozen.start,
    ),
    AppTableColumn(
      text: '操作',
      field: 'Opt',
      type: AppTableColumnType.text(),
      width: 80,
      resizable: false, // 允许调整列宽
      showMore: false,
      cellBuilder: (context, value, column, row) {
        return AppDropdown(
          items: [
            DropdownItem(text: '编辑', value: 'edit'),
            DropdownItem(text: '离职', value: 'dimission'),
            DropdownItem(text: '删除', value: 'delete'),
          ],
          trigger: DropdownTrigger.click,
          onItemSelected: (item) {
            final id = row['EmployeeId']?.toString();
            switch (item.value) {
              case 'edit':
                if (id != null) {
                  _memberDialogStateKey.currentState?.showDepartmentDialog(
                    context,
                    type: DialogTypeEmun.edit,
                    id: id,
                  );
                }
                break;
              case 'dimission':
                handleDimission(id!);
                break;
              case 'delete':
                deleteItem(id!);
                break;
              default:
            }
          },
          child: Icon(IconFont.xianxing_gengduo),
        );
      },
    ),
  ];

  /// 获取列表
  Future<void> getList() async {
    try {
      _stateManage.setLoading(true);
      print('请求的部门:${_selectedDepartment!.toJson()}');
      final response = await EmployeeApi.getListPage({'DepartmentId': _selectedDepartment?.id});

      List<dynamic> listJson = response['Items'];
      List<Map<String, dynamic>> jsonRowsStr =
          listJson.map((r) => r as Map<String, dynamic>).toList();

      var rows = _stateManage.convertToTreeData(jsonRowsStr);

      _stateManage.setRows(rows);
    } finally {
      _stateManage.setLoading(false);
    }
  }

  /// 删除
  Future<void> deleteItem(String id) async {
    showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text('提示'),
          content: const Text('您是否确定删除该项?'),
          actions: <Widget>[
            TextButton(child: const Text('取消'), onPressed: () => Navigator.of(context).pop(false)),
            TextButton(
              child: const Text('确定', style: TextStyle(color: AppColors.error)),
              onPressed: () async {
                final navigator = Navigator.of(context);
                try {
                  _stateManage.setLoading(true);
                  await EmployeeApi.delete([id]);
                  getList();
                } finally {
                  _stateManage.setLoading(false);
                }
                if (mounted) {
                  navigator.pop(true);
                }
              },
            ),
          ],
        );
      },
    );
  }

  // 离职时间选择弹窗
  void showDepartmentDialog(BuildContext context) async {
    AppDialog.show(
      width: 300,
      height: 150,
      context: context,
      title: '请选择离职时间',
      showFooter: false,
      child: StatefulBuilder(
        builder: (BuildContext context, StateSetter setDialogState) {
          return Container(width: 10, height: 10, color: Colors.amber);
          // return GestureDetector(
          //   onTap: () {
          //     OverlayEntry? over;
          //     over = OverlayEntry(
          //       maintainState: true,
          //       builder: (context) {
          //         return Center(
          //           child: TapRegion(
          //             onTapOutside: (event) {
          //               over?.remove();
          //               setState(() {});
          //             },
          //             child: AppDatetimePicker(
          //               onSelecting: (value) {},
          //               limitMinDate: null,
          //               limitMaxDate: null,
          //               isSingle: true,
          //               onSubmited: (value) {
          //                 print('时间选择完成$value');
          //                 over?.remove();
          //               },
          //               firstTitle: '',
          //               lastTitle: '',
          //             ),
          //           ),
          //         );
          //       },
          //     );
          //     Overlay.of(context).insert(over);
          //   },
          //   child: Container(width: 10, height: 10, color: Colors.red),
          // );
        },
      ),
    );
  }

  /// 离职
  Future<void> handleDimission(String id) async {
    showDepartmentDialog(context);
    /* final checkIdList = _stateManage.checkedRows.map((r) => r.id).toList();

    if (checkIdList.isEmpty) {
      ToastManager.error('请选择需要离职的成员');
      return;
    }
    // TODO:需要把时间换成时间选择
    // 当前时间
    DateTime now = DateTime.now();
    String formattedDate = DateFormat('yyyy-MM-dd').format(now);

    await EmployeeApi.dimission({'EmployeeIdList': checkIdList, 'DateLeft': formattedDate}); */
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧部门选择器
        Container(
          width: 250,
          decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
          child: DepartmentTree(
            key: _departmentSelectorKey,
            onNodeTap: (department) {
              // 处理部门节点点击事件（高亮显示）
              setState(() {
                _selectedDepartment = department;
              });
              getList();
              debugPrint('高亮选择了部门: ${department.departmentName}');
            },
          ),
        ),

        // 右侧操作区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              spacing: 10,
              children: [
                // 顶部区域
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _selectedDepartment?.departmentName ?? '',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Row(
                      spacing: 10,
                      children: [
                        AppButton(
                          iconData: IconFont.xianxing_bianji,
                          type: ButtonType.primary,
                          text: '调整部门',
                          onPressed: () {},
                        ),
                        CreateMemberDialog(
                          key: _memberDialogStateKey,
                          onSuccess: () => getList(),
                          child: AppButton(
                            iconData: IconFont.xianxing_tianjia,
                            text: '添加成员',
                            type: ButtonType.primary,
                            onPressed: () {
                              _memberDialogStateKey.currentState?.showDepartmentDialog(
                                context,
                                type: DialogTypeEmun.create,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Expanded(
                  child: AppTable(
                    uniqueId: 'Id',
                    showAddRowButton: false,
                    showAddColumnButton: false,
                    checkType: TableCheckedEnum.multiple,
                    indexColumnWidth: 100,
                    onLoaded: (stateManage) {
                      _stateManage = stateManage;
                      _stateManage.setColumns(colsTemp);
                    },
                    onLoadMore: () {
                      // loadMore();
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
