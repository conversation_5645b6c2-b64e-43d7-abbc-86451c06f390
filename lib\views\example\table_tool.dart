import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/app_table.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_model.dart';
import 'package:octasync_client/components/data/app_table/src/model/test_class.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/position_model.dart';

class TableTool extends StatefulWidget {
  const TableTool({super.key});

  @override
  State<TableTool> createState() => _TableToolState();
}

class _TableToolState extends State<TableTool> {
  List<String> tables = [];

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      getTabs();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧固定宽度区域
        SizedBox(
          width: 500, // 固定宽度200
          child: Column(
            children: [
              // 左侧标题
              Container(
                padding: const EdgeInsets.all(16.0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '当前已存在表格：',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              // 左侧列表区域
              Expanded(
                child: ListView.builder(
                  itemCount: 20, // 示例数据
                  itemBuilder: (context, index) {
                    return Container(child: Text('表格项目 $index'));
                    // return ListTile(title: Text('表格项目 $index'), subtitle: Text('表格描述 $index'));
                  },
                ),
              ),
            ],
          ),
        ),
        // 中间分割线
        const VerticalDivider(width: 1, color: Colors.grey),
        // 右侧自动撑开区域
        Expanded(
          child: Column(
            children: [
              // 右侧标题
              Container(
                padding: const EdgeInsets.all(16.0),
                alignment: Alignment.centerLeft,
                child: const Text(
                  '注册表格：',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              // 右侧内容区域
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('注册内容'),
                        const SizedBox(height: 16),
                        // 示例内容，足够多以触发滚动
                        ...List.generate(
                          50,
                          (index) => Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Text('这是注册内容的第 $index 行'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> getTabs() async {
    try {
      var postDatas = {"PageSize": 10000, "PageIndex": 1};
      final response = await AppTableApi.getListPage(postDatas);
      print('33333');
      print(response);
      print('333333333333333333333');

      // final pages = PagesModel<AppTableModel>.fromJson(
      //   response,
      //   (json) => AppTableModel.fromJson(json as Map<String, dynamic>),
      // );
      // print('44444');

      // print('111111111');
      // print(pages.items.length);

      // List<AppTableModel> tableModels2 =
      //     jsonList.map((item) => AppTableModel.fromJson(item)).toList();

      // print('tableModels2——————————————————————————————');
      // print(tableModels2);

      var jsonList = response['Items'];

      if (jsonList is! List) {
        throw Exception('Items 不是数组格式');
      }

      // 直接转换为 AppTableModel 集合
      List<AppTableModel> tableModels =
          jsonList
              .map((item) => _parseAppTableModel(item))
              .where((model) => model != null)
              .cast<AppTableModel>()
              .toList();

      var result = tableModels.map((t) => t.smartTableHeaderId).toList();
      print('22222220000000000');

      print(tableModels);

      try {
        print(tableModels[0].toJson());
      } catch (e) {
        print('错误信息：$e');
      }

      setState(() {
        tables = result;
      });
    } catch (e) {
      print('getTabs 异常: $e');
    }
  }

  /// 解析单个 AppTableModel
  AppTableModel? _parseAppTableModel(Map<String, dynamic> data) {
    try {
      // 预处理数据，处理 HeaderSet 字符串
      Map<String, dynamic> processedData = Map<String, dynamic>.from(data);

      var headerSetData = data['HeaderSet'];

      if (headerSetData is String) {
        // 如果是字符串，解析为 List
        List<dynamic> headerSetList = jsonDecode(headerSetData);
        processedData['HeaderSet'] = headerSetList;
      } else if (headerSetData is! List) {
        // print('HeaderSet 数据格式不支持: ${headerSetData.runtimeType}');
        return null;
      }

      // 确保有 smartTableHeaderId 字段
      if (!processedData.containsKey('smartTableHeaderId')) {
        processedData['smartTableHeaderId'] = processedData['SmartTableHeaderId'] ?? '';
      }

      // 确保有 headerSet 字段
      if (!processedData.containsKey('headerSet')) {
        processedData['headerSet'] = processedData['HeaderSet'] ?? [];
      }

      // print('处理后的数据: $processedData');

      return AppTableModel.fromJson(processedData);
    } catch (e) {
      // print('解析 AppTableModel 失败: $e');
      // print('原始数据: $data');
      return null;
    }
  }
}
